<script lang="ts">
  import { onMount } from 'svelte';
  import { TrendingUp, Users, Calendar, Badge } from 'lucide-svelte';
  import * as Chart from '$lib/components/ui/chart';
  import { BarChart } from 'layerchart';

  let { referralData } = $props<{
    referralData: any;
  }>();

  let analyticsData = $state<any>(null);
  let loadingAnalytics = $state(true);

  // Load analytics data
  const loadAnalytics = async () => {
    try {
      const response = await fetch('/api/referrals/analytics');
      if (response.ok) {
        analyticsData = await response.json();
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      loadingAnalytics = false;
    }
  };

  // Load analytics on component mount
  onMount(() => {
    loadAnalytics();
  });

  // Reactive derived values
  const monthlyData = $derived(analyticsData?.monthlyAnalytics || []);

  // Chart configuration
  const chartConfig = {
    referrals: {
      label: 'Monthly Referrals',
      color: 'var(--chart-1)',
    },
    cumulative: {
      label: 'Cumulative',
      color: 'var(--chart-2)',
    },
  } satisfies Chart.ChartConfig;
</script>

<!-- Chart Header -->
<div class="border-border flex items-end justify-between border-b p-4">
  <div class="flex flex-col">
    <h3 class="text-lg font-semibold">Referral Analytics</h3>
    <p class="text-muted-foreground text-sm">Track your referral performance over time</p>
  </div>
  <div class="flex items-center gap-2">
    <TrendingUp class="text-primary h-5 w-5" />
    <span class="text-sm font-medium">Growth Trend</span>
  </div>
</div>

<!-- Chart Container -->
<div class="border-border grid grid-cols-1 gap-4 divide-x border-b sm:grid-cols-3">
  <div class="p-4">
    <div class="flex items-center gap-2">
      <Users class="text-primary h-4 w-4" />
      <span class="text-sm font-medium">Total Referrals</span>
    </div>
    <div class="mt-2">
      <div class="text-2xl font-bold">{referralData?.referralCount || 0}</div>
      <div class="text-muted-foreground text-xs">All time</div>
    </div>
  </div>

  <div class="p-4">
    <div class="flex items-center gap-2">
      <Calendar class="text-success h-4 w-4" />
      <span class="text-sm font-medium">This Month</span>
    </div>
    <div class="mt-2">
      <div class="text-2xl font-bold">
        {loadingAnalytics ? '...' : monthlyData[monthlyData.length - 1]?.referrals || 0}
      </div>
      <div class="text-muted-foreground text-xs">New referrals</div>
    </div>
  </div>

  <div class="p-4">
    <div class="flex items-center gap-2">
      <TrendingUp class="text-warning h-4 w-4" />
      <span class="text-sm font-medium">Success Rate</span>
    </div>
    <div class="mt-2">
      <div class="text-2xl font-bold">
        {loadingAnalytics ? '...' : analyticsData?.conversionRate || 0}%
      </div>
      <div class="text-muted-foreground text-xs">Conversion rate</div>
    </div>
  </div>
</div>

<!-- Chart -->
<div class="h-64 p-4">
  <div class="mb-4 flex items-center justify-between">
    <h4 class="font-medium">Monthly Referrals</h4>
    <div class="flex items-center gap-4 text-xs">
      <div class="flex items-center gap-1">
        <div class="h-3 w-3 rounded-full" style="background-color: var(--chart-1);"></div>
        <span>Monthly</span>
      </div>
      <div class="flex items-center gap-1">
        <div class="h-3 w-3 rounded-full" style="background-color: var(--chart-2);"></div>
        <span>Cumulative</span>
      </div>
    </div>
  </div>

  {#if loadingAnalytics}
    <div class="flex h-48 items-center justify-center">
      <div class="text-muted-foreground text-sm">Loading chart data...</div>
    </div>
  {:else if monthlyData.length === 0}
    <div class="flex h-48 items-center justify-center">
      <div class="text-muted-foreground text-sm">No data available</div>
    </div>
  {:else}
    <Chart.Container config={chartConfig} class="h-48 w-full">
      <BarChart
        data={monthlyData}
        x="month"
        axis="x"
        legend
        series={[
          {
            key: 'referrals',
            label: chartConfig.referrals.label,
            color: chartConfig.referrals.color,
          },
        ]}
        props={{
          xAxis: {
            format: (d) => d.slice(0, 3),
          },
        }}>
        {#snippet tooltip()}
          <Chart.Tooltip />
        {/snippet}
      </BarChart>
    </Chart.Container>
  {/if}
</div>

<!-- Referral Code History -->
<div class="bg-secondary m-4 space-y-4 rounded-md p-4">
  <div class="flex flex-col">
    <h4 class="flex gap-2 text-sm">
      <TrendingUp class="h-4 w-4" />
      Referral Code History
    </h4>
    <p class="text-muted-foreground text-xs">
      Track all your referral codes and their performance over time.
    </p>
  </div>
  <div>
    {#if loadingAnalytics}
      <div class="text-muted-foreground text-center text-sm">Loading referral code history...</div>
    {:else if analyticsData?.codeHistory?.length > 0}
      <div class="space-y-4">
        {#each analyticsData.codeHistory as codeEntry}
          <div class="rounded-lg border p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="font-mono text-lg font-semibold">
                  {codeEntry.referralCode}
                </div>
                {#if codeEntry.isActive}
                  <Badge class="bg-primary text-white">Current</Badge>
                {:else}
                  <Badge class="bg-secondary text-muted-foreground">Historical</Badge>
                {/if}
              </div>
              <div class="text-right">
                <div class="text-sm font-medium">
                  {codeEntry.referralCount} referrals
                </div>
                <div class="text-muted-foreground text-xs">
                  {codeEntry.completedReferrals} completed, {codeEntry.pendingReferrals}
                  pending
                </div>
              </div>
            </div>

            <div class="mt-3 grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-muted-foreground">Created:</span>
                {new Date(codeEntry.createdAt).toLocaleDateString()}
              </div>
              {#if codeEntry.deactivatedAt}
                <div>
                  <span class="text-muted-foreground">Deactivated:</span>
                  {new Date(codeEntry.deactivatedAt).toLocaleDateString()}
                </div>
              {/if}
            </div>

            {#if codeEntry.referrals?.length > 0}
              <div class="mt-3">
                <div class="text-muted-foreground mb-2 text-xs">
                  Recent referrals with this code:
                </div>
                <div class="space-y-1">
                  {#each codeEntry.referrals.slice(0, 3) as referral}
                    <div class="flex items-center justify-between text-xs">
                      <span
                        >{referral.referred?.name ||
                          referral.referred?.email ||
                          'Unknown User'}</span>
                      <Badge
                        class={`text-xs ${referral.status === 'completed' ? 'bg-primary text-white' : 'bg-secondary text-muted-foreground'}`}>
                        {referral.status || 'pending'}
                      </Badge>
                    </div>
                  {/each}
                  {#if codeEntry.referrals.length > 3}
                    <div class="text-muted-foreground text-xs">
                      +{codeEntry.referrals.length - 3} more referrals
                    </div>
                  {/if}
                </div>
              </div>
            {/if}
          </div>
        {/each}
      </div>
    {:else}
      <div class="text-muted-foreground text-center text-sm">
        No referral code history yet. Generate referrals with your current code to see analytics
        here.
      </div>
    {/if}
  </div>
</div>
